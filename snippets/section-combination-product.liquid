{% # components v3.0.1 | Copyright © 2024 Archetype Themes Limited Partnership  | "Shopify Theme Store (https://www.shopify.com/legal/terms#9-additional-services)" License  %}
{%- comment -%}
  Renders a section with hotspots.

  Accepts:
  - title {string} - The title of the section
  - heading_size {'h0'|'h1'|'h2'|'h3'} - The size of the heading
  - heading_position {'left'|'center'|'right'} - The position of the heading
  - image {image} - The image of the section
  - indent_image {boolean} - Whether to indent the image
  - image_position {'left'|'right'} - The position of the image
  - hotspot_style {'dot'|'plus'|'tag'|'bag'} - The style of the hotspot
  - hotspot_color {color} - The color of the hotspot
  - hydration {string} - The hydration strategy of the section

  Usage:
  {% render 'combination-product', title: 'Shop collections' %}
{%- endcomment -%}

{%- liquid
  assign title = title | default: section.settings.title
  assign heading_size = heading_size | default: section.settings.heading_size | default: 'h2'
  assign heading_position = heading_position | default: section.settings.heading_position | default: 'center'
  assign image = image | default: section.settings.image
  assign indent_image = indent_image | default: section.settings.indent_image, allow_false: true | default: false, allow_false: true
  assign image_position = image_position | default: section.settings.image_position | default: 'left'
  assign hotspot_style = hotspot_style | default: section.settings.hotspot_style | default: 'bag'
  assign hotspot_color = hotspot_color | default: section.settings.hotspot_color | default: '#000'
  assign hydration = hydration | default: 'on:visible'
-%}

{%- liquid
  assign lazyload_images = true

  if section.index == 1
    assign lazyload_images = false
  endif
-%}



<div class="index-section">
  {% if title != blank %}
    <div class="page-width">
      <h2 class="hotspots__title {{ heading_size }} text-{{ heading_position }}">{{ title | escape }}</h2>
    </div>
  {% endif %}

  <div class="hotspots-wrapper {% unless indent_image %}page-width{% endunless %} {% if image_position == 'right' %}is-reverse{% endif %}">
    <div class="hotspots">
      <div class="hotspots__image hotspots__image--indent-{{ indent_image }}">
        <div class="grid__image-ratio grid__image-ratio--square">
          {% if image != blank %}
            {%- render 'image-element',
              img: image,
              img_width: 2400,
              loading: lazyload_images,
              sizeVariable: '70vw'
            -%}
          {% else %}
            {%- render 'placeholder-svg', name: 'lifestyle-1' -%}
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
