<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shop by Category</title>
    <style>
        .category-shop {
            background: #FFFFFF;
            padding: 64px 100px;
        }
        .category-shop .section-title {
            font-size: 1.354vw; /* 26px based on 1920px */
            line-height: 100%;
            letter-spacing: 0px;
            margin-bottom: 30px;
            font-family: Playfair Display;
            font-weight: 400;
            font-style: Regular;
            leading-trim: NONE;
        }


        .category-shop .carousel-wrapper {
            position: relative;
            display: flex;
            align-items: flex-start; /* 改为顶部对齐，让箭头可以精确定位到图片中心 */
        }

        .category-shop .carousel-viewport {
            overflow: hidden; /* 隐藏超出的内容，只显示4个产品 */
            width: calc(4 * 20.677vw + 3 * 2.292vw); /* 4个产品(20.677vw) + 3个间距(2.292vw = 44px) based on 1920px */
            flex: 1;
            margin: 0 auto; /* 居中显示轮播区域 */
        }

        .category-shop .carousel-container {
            display: flex;
            align-items: flex-start; /* 改为顶部对齐，让产品卡片自然排列 */
            transition: transform 0.3s ease;
        }

        .category-shop .carousel-arrow {
            display: flex; /* Web端默认显示，移动端通过媒体查询隐藏 */
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            z-index: 10;
            position: absolute; /* 改为绝对定位 */
            top: 10.339vw; /* 定位到产品图片中心：图片高度一半(20.677vw / 2) */
            background: transparent; /* 移除默认背景，让SVG样式生效 */
            width: 2.083vw; /* 40px */
            height: 2.083vw; /* 40px */
            transform: translateY(-50%); /* 垂直居中 */
        }

        .category-shop .carousel-arrow svg {
            width: 100%;
            height: 100%;
        }

        .category-shop .carousel-arrow.show {
            display: flex; /* 当需要显示时 */
        }

        .category-shop .carousel-arrow:hover {
            background: transparent; /* 移除背景，让SVG样式生效 */
            transform: translateY(-50%) scale(1.1); /* 保持垂直居中的同时放大 */
        }

        .category-shop .carousel-arrow:disabled {
            background: transparent; /* 移除背景，让SVG样式生效 */
            cursor: not-allowed;
            transform: translateY(-50%); /* 保持垂直居中 */
            opacity: 1; /* 确保禁用状态完全不透明 */
        }

        /* 导航按钮SVG样式 */
        /* 默认状态（能点击） */
        .category-shop .carousel-arrow svg circle {
            fill: #ffffff; /* 背景颜色 */
            fill-opacity: 1; /* 确保不透明 */
            stroke: #6D4C41; /* 边框颜色 */
            transition: fill 0.3s ease, stroke 0.3s ease;
        }

        .category-shop .carousel-arrow svg path {
            stroke: #6D4C41; /* 箭头颜色 */
            transition: stroke 0.3s ease;
        }

        /* 禁用状态（不能点击） */
        .category-shop .carousel-arrow:disabled svg circle {
            fill: #ffffff !important; /* 背景颜色，确保不透明 */
            fill-opacity: 1 !important; /* 确保完全不透明 */
            stroke: #CCCCCC !important; /* 边框颜色 */
        }

        .category-shop .carousel-arrow:disabled svg path {
            stroke: #CCCCCC; /* 箭头颜色 */
        }

        /* 悬停状态（鼠标放上去） */
        .category-shop .carousel-arrow:not(:disabled):hover svg circle {
            fill: #6D4C41; /* 背景颜色 */
            fill-opacity: 1; /* 确保不透明 */
            stroke: #6D4C41; /* 边框颜色保持一致 */
        }

        .category-shop .carousel-arrow:not(:disabled):hover svg path {
            stroke: #ffffff; /* 箭头颜色 */
        }

        /* 确保禁用状态的按钮完全不透明 */
        .category-shop .carousel-arrow:disabled {
            opacity: 1;
            background: transparent; /* 移除背景色，让SVG样式生效 */
        }



        .category-shop .carousel-arrow.prev {
            left: -1.042vw; /* 左边第一个图片左边框位置，箭头中心对齐边框 */
        }

        .category-shop .carousel-arrow.next {
            right: -1.042vw; /* 右边第四个图片右边框位置，箭头中心对齐边框 */
        }



        .category-shop .product-card,
        .category-shop .shop-category-product-card {
            flex: 0 0 20.677vw; /* 397px based on 1920px */
            text-align: center;
            position: relative;
            transition: transform 0.3s ease;
            margin-right: 2.292vw; /* 44px based on 1920px */
        }

        /* 动态控制右边距的CSS类 */
        .category-shop .product-card.no-right-margin,
        .category-shop .shop-category-product-card.no-right-margin {
            margin-right: 0 !important;
        }

        .category-shop .product-card a,
        .category-shop .shop-category-product-card a {
            text-decoration: none;
            cursor: pointer;
        }

        .category-shop .product-card img,
        .category-shop .shop-category-product-card img {
            width: 20.677vw; /* 397px based on 1920px */
            height: 20.677vw; /* 397px based on 1920px */
            transition: transform 0.3s ease;
            object-fit: cover;
        }

        /* 响应式图片显示控制 */
        .category-shop .product-card .desktop-image,
        .category-shop .shop-category-product-card .desktop-image {
            display: block; /* 桌面端显示 */
        }

        .category-shop .product-card .mobile-image,
        .category-shop .shop-category-product-card .mobile-image {
            display: none; /* 桌面端隐藏移动端图片 */
        }

        .category-shop .product-card a:hover img,
        .category-shop .shop-category-product-card a:hover img {
            transform: scale(1.05);
        }



        .category-shop .btn {
            color: #6D4C41;
            border-radius: 3.125vw; /* 60px based on 1920px */
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            margin: 0 auto;
            transition: background 0.3s ease;
            margin-top: 24px;

            width: 166px;
            height: 38px;
            border: 1px solid #6D4C41;
            font-family: Playfair Display;
            font-weight: 400;
            font-style: Regular;
            font-size: 16px;
            leading-trim: NONE;
            line-height: 100%;
            background: transparent;
        }

        .category-shop .btn:hover {
            background: #502212;
            color: #F3E8DD;
        }

        @media (max-width: 768px) {
            .category-shop {
                padding: 2.083vw 0 2.995vw 2.604vw; /* 16px 0 23px 20px based on 768px */
            }

            .category-shop .section-title {
                font-family: Playfair Display;
                font-weight: 400;
                font-style: Regular;
                font-size: 2.604vw; /* 20px based on 768px */
                leading-trim: NONE;
                line-height: 100%;
                letter-spacing: 0px;
                margin-bottom: 1.823vw; /* 14px based on 768px */
                margin-left: 0; /* 移除左边距 */
            }

            .category-shop .carousel-viewport {
                /*width: calc(2.5 * 18.229vw + 2.5 * 1.302vw); !* 2.5个产品显示宽度 based on 768px *!*/
                width: 100%;
                overflow-x: auto; /* 支持水平滚动 */
                -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
                scrollbar-width: none; /* Firefox隐藏滚动条 */
                -ms-overflow-style: none; /* IE隐藏滚动条 */
            }

            .category-shop .carousel-viewport::-webkit-scrollbar {
                display: none; /* Webkit隐藏滚动条 */
            }

            .category-shop .carousel-container {
                /* 移除gap，改用margin-right */
            }

            .category-shop .carousel-arrow {
                display: none;
            }

            .category-shop .carousel-arrow svg {
                display: none; /* 移动端隐藏轮播箭头SVG */
            }
            .category-shop .carousel-arrow.prev {
                margin-right: 0;
            }

            .category-shop .carousel-arrow.next {
                margin-left: 0;
            }
            .category-shop .carousel-arrow.show {
                display: none;
            }


            .category-shop .product-card,
            .category-shop .shop-category-product-card {
                flex: 0 0 26.042vw; /* 200px based on 768px */
                width: 26.042vw; /* 200px based on 768px */
                margin-right: 2.083vw; /* 16px based on 768px */
            }

            /* 移除固定的第四个产品样式，改为JavaScript动态控制 */

            .category-shop .product-card:last-child,
            .category-shop .shop-category-product-card:last-child {
                margin-right: 3.906vw; /* 最后一个产品添加右边距，确保滚动时有足够空间 based on 768px */
            }

            .category-shop .product-card img,
            .category-shop .shop-category-product-card img {
                width: 26.042vw; /* 200px based on 768px */
                height: 26.042vw; /* 200px based on 768px */
                object-fit: cover;
            }

            /* 移动端图片显示控制 */
            .category-shop .product-card .desktop-image,
            .category-shop .shop-category-product-card .desktop-image {
                display: none; /* 移动端隐藏桌面端图片 */
            }

            .category-shop .product-card .mobile-image,
            .category-shop .shop-category-product-card .mobile-image {
                display: block; /* 移动端显示移动端图片 */
            }



            .category-shop .btn {
                width: 16.667vw; /* 128px based on 768px */
                height: 4.688vw; /* 36px based on 768px */
                border-radius: 3.906vw; /* 30px based on 768px */
                border: 0.130vw solid; /* 1px based on 768px */
                font-family: Playfair Display;
                font-weight: 400;
                font-style: Regular;
                font-size: 1.823vw; /* 14px based on 768px */
                leading-trim: NONE;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                margin-top: 2.083vw; /* 16px based on 768px */
                position: relative;
                margin-left: auto;
                margin-right: auto;
            }
        }

        @media (max-width: 480px) {
            .category-shop {
                padding: 4.267vw 0 6.133vw 5.333vw; /* 16px 0 23px 20px based on 375px */
            }

            .category-shop .section-title {
                font-family: Playfair Display;
                font-weight: 400;
                font-style: Regular;
                font-size: 5.333vw; /* 20px based on 375px */
                leading-trim: NONE;
                line-height: 100%;
                letter-spacing: 0px;
                margin-bottom: 3.733vw; /* 14px based on 375px */
                margin-left: 0; /* 移除左边距 */
            }

            .category-shop .carousel-viewport {
                /*width: calc(2.5 * 37.333vw + 2.5 * 2.667vw); !* 2.5个产品显示宽度 based on 375px *!*/
                width: 100%;
                overflow-x: auto; /* 支持水平滚动 */
                -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
                scrollbar-width: none; /* Firefox隐藏滚动条 */
                -ms-overflow-style: none; /* IE隐藏滚动条 */
            }

            .category-shop .carousel-viewport::-webkit-scrollbar {
                display: none; /* Webkit隐藏滚动条 */
            }

            .category-shop .carousel-container {
                /* 移除gap，改用margin-right */
            }

            .category-shop .carousel-arrow {
                display: none;
            }

            .category-shop .carousel-arrow svg {
                display: none; /* 移动端隐藏轮播箭头SVG */
            }
            .category-shop .carousel-arrow.prev {
                margin-right: 0;
            }

            .category-shop .carousel-arrow.next {
                margin-left: 0;
            }
            .category-shop .carousel-arrow.show {
                display: none;
            }


            .category-shop .product-card,
            .category-shop .shop-category-product-card {
                flex: 0 0 53.333vw; /* 200px based on 375px */
                width: 53.333vw; /* 200px based on 375px */
                margin-right: 4.267vw; /* 16px based on 375px */
            }

            /* 移除固定的第四个产品样式，改为JavaScript动态控制 */

            .category-shop .product-card:last-child,
            .category-shop .shop-category-product-card:last-child {
                margin-right: 8vw; /* 最后一个产品添加右边距，确保滚动时有足够空间 based on 375px */
            }

            .category-shop .product-card img,
            .category-shop .shop-category-product-card img {
                width: 53.333vw; /* 200px based on 375px */
                height: 53.333vw; /* 200px based on 375px */
                object-fit: cover;
            }

            /* 移动端图片显示控制 */
            .category-shop .product-card .desktop-image,
            .category-shop .shop-category-product-card .desktop-image {
                display: none; /* 移动端隐藏桌面端图片 */
            }

            .category-shop .product-card .mobile-image,
            .category-shop .shop-category-product-card .mobile-image {
                display: block; /* 移动端显示移动端图片 */
            }



            .category-shop .btn {
                width: 34.133vw; /* 128px based on 375px */
                height: 9.6vw; /* 36px based on 375px */
                border-radius: 8vw; /* 30px based on 375px */
                border: 0.267vw solid; /* 1px based on 375px */
                font-family: Playfair Display;
                font-weight: 400;
                font-style: Regular;
                font-size: 3.733vw; /* 14px based on 375px */
                leading-trim: NONE;
                line-height: 100%;
                letter-spacing: 0px;
                text-align: center;
                margin-top: 4.267vw; /* 16px based on 375px */
                position: relative;
                margin-left: auto;
                margin-right: auto;
            }
        }

        @media screen and (max-width: 768px) {
            .category-shop .carousel-wrapper {
                /*flex-direction: column;*/
            }

            .category-shop .carousel-arrow {
                display: none !important; /* 移动端完全隐藏箭头容器，不占据空间 */
            }

            .category-shop .carousel-arrow svg {
                display: none; /* 移动端隐藏轮播箭头SVG */
            }

            .category-shop .carousel-arrow.show {
                display: none !important; /* 移动端即使有show类也不显示箭头，不占据空间 */
            }

            .category-shop .carousel-container {
                flex-wrap: nowrap; /* 保持产品在一行 */
                justify-content: flex-start; /* 左对齐 */
                width: max-content; /* 容器宽度适应内容 */
            }

        }
    </style>
</head>
<body>
<div class="category-shop">
    <div class="section-title">{{ section.settings.title }}</div>
    <div class="carousel-wrapper">
        <button class="carousel-arrow prev" id="shopCategoryPrevBtn">
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="20" r="19.5" fill="#ffffff" stroke="#6D4C41"/>
                <path d="M22 14L16 20L22 26" stroke="#6D4C41" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
        <div class="carousel-viewport">
            <div class="carousel-container" id="shopCategoryCarouselContainer">
            {% for block in section.blocks %}
                {% assign product = block.settings.product %}
                {% if product != blank %}
                    <div class="product-card shop-category-product-card">
                        {% comment %} 产品图片 - 响应式显示 {% endcomment %}
                        {% assign image_url = block.settings.image_url | default: product.url %}
                        <a href="{{ image_url }}">
                            {% comment %} 桌面端图片 {% endcomment %}
                            {% assign desktop_image = block.settings.desktop_image | default: product.featured_image %}
                            {% if desktop_image %}
                                <img class="desktop-image" src="{{ desktop_image }}" alt="{{ desktop_image.alt | default: product.title }}">
                            {% endif %}

                            {% comment %} 移动端图片 {% endcomment %}
                            {% assign mobile_image = block.settings.mobile_image | default: block.settings.desktop_image | default: product.featured_image %}
                            {% if mobile_image %}
                                <img class="mobile-image" src="{{ mobile_image }}" alt="{{ mobile_image.alt | default: product.title }}">
                            {% endif %}
                        </a>

                        {% comment %} 购买按钮 {% endcomment %}
                        {% assign button_url = block.settings.button_url | default: product.url %}
                        <a href="{{ button_url }}" class="btn">{{ block.settings.button_text | default: 'Nightstands' }}</a>
                    </div>
                {% endif %}
            {% endfor %}
            </div>
        </div>
        <button class="carousel-arrow next" id="shopCategoryNextBtn">
            <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="20" r="19.5" fill="#ffffff" stroke="#6D4C41"/>
                <path d="M18 14L24 20L18 26" stroke="#6D4C41" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Shop by Category: Initializing...');

    const carouselContainer = document.getElementById('shopCategoryCarouselContainer');
    const prevBtn = document.getElementById('shopCategoryPrevBtn');
    const nextBtn = document.getElementById('shopCategoryNextBtn');

    console.log('DOM elements found:', {
        carouselContainer: !!carouselContainer,
        prevBtn: !!prevBtn,
        nextBtn: !!nextBtn
    });

    // 添加防护检查，确保DOM元素存在
    if (!carouselContainer || !prevBtn || !nextBtn) {
        console.warn('Shop by Category: Required DOM elements not found');
        return;
    }

    const productCards = carouselContainer.querySelectorAll('.shop-category-product-card');
    console.log('Product cards found:', productCards.length);

    // 检查是否有产品卡片
    if (!productCards || productCards.length === 0) {
        console.warn('Shop by Category: No product cards found');
        return;
    }

    // 响应式计算宽度 - 与CSS中的实际尺寸匹配
    function getResponsiveValues() {
        const screenWidth = window.innerWidth;

        // 防护检查
        if (!screenWidth || screenWidth <= 0) {
            console.warn('Invalid screen width:', screenWidth);
            return { cardWidth: 397, gap: 44 }; // 默认值
        }

        let cardWidth, gap;

        if (screenWidth <= 480) {
            // 基于375px计算 - 与CSS匹配
            cardWidth = screenWidth * 0.53333; // 53.333vw (200px) - 匹配CSS
            gap = screenWidth * 0.04267; // 4.267vw (16px) - 匹配CSS
        } else if (screenWidth <= 768) {
            // 基于768px计算 - 与CSS匹配
            cardWidth = screenWidth * 0.26042; // 26.042vw (200px) - 匹配CSS
            gap = screenWidth * 0.02083; // 2.083vw (16px) - 匹配CSS
        } else {
            // 基于1920px计算 - 修正为与CSS匹配
            cardWidth = screenWidth * 0.20677; // 20.677vw (397px) - 匹配CSS中的产品卡片宽度
            gap = screenWidth * 0.02292; // 2.292vw (44px) - 匹配CSS
        }

        return { cardWidth, gap };
    }

    const maxVisibleItems = 4; // 最多显示4个产品

    let currentIndex = 0;
    const totalItems = productCards.length;

    // 检查是否为移动端
    function isMobile() {
        return window.innerWidth <= 768;
    }

    // 移动端检查暂时禁用，先测试基本功能
    // if (isMobile()) {
    //     console.log('Mobile device detected, disabling carousel');
    //     if (prevBtn) prevBtn.disabled = true;
    //     if (nextBtn) nextBtn.disabled = true;
    //     return;
    // }

    // 如果产品数量 <= 4，禁用箭头；否则启用箭头
    if (totalItems <= maxVisibleItems) {
        if (prevBtn) prevBtn.disabled = true;
        if (nextBtn) nextBtn.disabled = true;
        return;
    } else {
        if (prevBtn) prevBtn.disabled = false;
        if (nextBtn) nextBtn.disabled = false;
    }

    // 更新轮播位置 - 简化版本
    function updateCarousel() {
        console.log('updateCarousel called, currentIndex:', currentIndex);

        // 基本检查
        if (!carouselContainer) {
            console.warn('CarouselContainer not found');
            return;
        }

        // 简单的固定计算，避免复杂的响应式逻辑
        const cardWidth = 397; // 固定宽度
        const gap = 44; // 固定间距
        const itemWidth = cardWidth + gap;
        const translateX = -currentIndex * itemWidth;

        console.log('Simple calculation:', { currentIndex, itemWidth, translateX });

        // 应用变换
        carouselContainer.style.transform = `translateX(${translateX}px)`;

        // 更新按钮状态
        if (prevBtn) prevBtn.disabled = (currentIndex === 0);
        if (nextBtn) nextBtn.disabled = (currentIndex + maxVisibleItems >= totalItems);

        console.log('Button states updated');
    }

    // 上一组产品（4个）
    prevBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Prev clicked - Current index:', currentIndex, 'Total items:', totalItems);
        if (currentIndex > 0) {
            currentIndex = Math.max(0, currentIndex - maxVisibleItems);
            console.log('Moving to index:', currentIndex);
            updateCarousel();
        }
    });

    // 下一组产品（4个）
    nextBtn.addEventListener('click', function(e) {
        e.preventDefault();
        console.log('Next clicked - Current index:', currentIndex, 'Total items:', totalItems);
        if (currentIndex + maxVisibleItems < totalItems) {
            currentIndex = currentIndex + maxVisibleItems;
            console.log('Moving to index:', currentIndex);
            updateCarousel();
        } else {
            console.log('Cannot move next - already at last group');
        }
    });

    // 窗口大小变化时重新计算
    window.addEventListener('resize', function() {
        updateCarousel();
    });

    // 初始化
    updateCarousel();

    console.log('Shop by Category: Initialization completed successfully');
});
</script>

</body>
</html>

{% schema %}
{
  "name": "Shop by Category",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Shop by Category"
    }
  ],
  "blocks": [
    {
      "type": "product_card",
      "name": "Product Card",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Select Product"
        },
        {
          "type": "image_picker",
          "id": "desktop_image",
          "label": "Desktop Image",
          "info": "Custom image for desktop display. Leave blank to use product featured image."
        },
        {
          "type": "image_picker",
          "id": "mobile_image",
          "label": "Mobile Image",
          "info": "Custom image for mobile display. Leave blank to use desktop image or product featured image."
        },
        {
          "type": "url",
          "id": "image_url",
          "label": "Image Link URL",
          "info": "Custom URL for image click. Leave blank to use product URL."
        },
        {
          "type": "url",
          "id": "button_url",
          "label": "Button Link URL",
          "info": "Custom URL for button click. Leave blank to use product URL."
        },
        {
          "type": "text",
          "id": "button_text",
          "label": "Button Text",
          "default": "Nightstands",
          "info": "Custom text for the button."
        }
      ]
    }
  ],
  "max_blocks": 8,
  "presets": [
    {
      "name": "Shop by Category",
      "category": "Custom",
      "blocks": [
        {
          "type": "product_card"
        },
        {
          "type": "product_card"
        },
        {
          "type": "product_card"
        }
      ]
    }
  ]
}
{% endschema %}
